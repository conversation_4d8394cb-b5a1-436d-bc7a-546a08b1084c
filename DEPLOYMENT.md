# Pulse Guard 一键部署指南

## 🚀 快速开始

### 1. 准备环境

确保已安装：
- Docker
- Docker Compose

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件，填入必要的 API 密钥
vim .env
```

**必须配置的环境变量：**
- `GITHUB_TOKEN`: GitHub API 令牌
- `DEEPSEEK_API_KEY`: Deepseek API 密钥

### 3. 一键部署

```bash
# 启动所有服务
./deploy.sh

# 或者
./deploy.sh start
```

### 4. 验证部署

```bash
# 检查服务状态
./deploy.sh status

# 查看日志
./deploy.sh logs

# 访问应用
curl http://localhost:8000/api/health
```

## 本地开发部署

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd pulse-guard

# 安装 uv (推荐的包管理器)
curl -sSf https://install.uv.dev | sh

# 创建虚拟环境
uv venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows
```

### 2. 安装依赖

```bash
# 安装项目依赖
make install

# 或安装开发依赖
make dev
```

### 3. 配置环境

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入必要的配置
vim .env
```

必须配置的环境变量：
- `GITHUB_TOKEN`: GitHub API 令牌
- `DEEPSEEK_API_KEY`: Deepseek API 密钥
- `REDIS_URL`: Redis 连接URL (默认: redis://localhost:6379/0)

### 4. 启动服务

#### 方式一：使用 Makefile (推荐)

```bash
# 终端1: 启动 Redis (如果未运行)
redis-server

# 终端2: 启动 Web 服务
make run

# 终端3: 启动 Celery Worker
make worker

# 终端4: 启动 ngrok (用于本地测试 webhook)
make ngrok
```

#### 方式二：手动启动

```bash
# 启动 Web 服务
uv run uvicorn pulse_guard.main:app --host 0.0.0.0 --port 8000 --reload

# 启动 Celery Worker
uv run python celery_worker.py
```

### 5. 验证部署

```bash
# 检查 Web 服务
curl http://localhost:8000/

# 检查健康状态
curl http://localhost:8000/api/health
```

## Docker 部署

### 1. 快速启动 (开发环境)

```bash
# 复制环境变量文件
cp .env.example .env
# 编辑 .env 文件

# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 2. 生产环境部署

```bash
# 使用生产环境配置
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 或者使用预构建镜像
docker-compose -f docker-compose.prod.yml up -d
```

### 3. 服务说明

Docker 部署包含以下服务：

- **web**: FastAPI Web 服务 (端口 8000)
- **worker**: Celery Worker 服务
- **redis**: Redis 消息队列和缓存
- **nginx**: 反向代理 (生产环境，端口 80/443)

### 4. 扩展服务

```bash
# 扩展 worker 实例
docker-compose up -d --scale worker=3

# 扩展 web 实例 (需要负载均衡器)
docker-compose up -d --scale web=2
```

## 生产环境部署

### 1. 服务器准备

```bash
# 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置文件准备

```bash
# 创建项目目录
sudo mkdir -p /opt/pulse-guard
cd /opt/pulse-guard

# 复制项目文件
# ... (通过 git clone 或文件传输)

# 配置环境变量
cp .env.example .env
sudo vim .env
```

### 3. SSL 证书配置

```bash
# 使用 Let's Encrypt (推荐)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 或者使用自签名证书 (仅测试)
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/pulse-guard.key \
  -out /etc/ssl/certs/pulse-guard.crt
```

### 4. 启动生产服务

```bash
# 启动服务
docker-compose -f docker-compose.prod.yml up -d

# 设置开机自启
sudo systemctl enable docker
```

### 5. 配置反向代理

如果使用外部 Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/pulse-guard.crt;
    ssl_certificate_key /etc/ssl/private/pulse-guard.key;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 配置说明

### 环境变量配置

| 变量名 | 必需 | 说明 | 示例 |
|--------|------|------|------|
| `GITHUB_TOKEN` | 是 | GitHub API 令牌 | `ghp_xxxxxxxxxxxx` |
| `DEEPSEEK_API_KEY` | 是 | Deepseek API 密钥 | `sk-xxxxxxxxxxxx` |
| `REDIS_URL` | 否 | Redis 连接URL | `redis://localhost:6379/0` |
| `WEBHOOK_SECRET` | 推荐 | GitHub Webhook 密钥 | `your-secret-key` |
| `GITEE_ACCESS_TOKEN` | 可选 | Gitee API 令牌 | `xxxxxxxxxxxx` |

### config.toml 配置

```toml
[llm]
provider = "deepseek"
model_name = "deepseek-chat"

[github]
api_base_url = "https://api.github.com"
development_mode = false          # 生产环境设为 false
verify_webhook_signature = true   # 生产环境设为 true

[review]
types = ["code_quality", "security", "best_practices"]
max_files_per_review = 10
```

## 监控和日志

### 1. 日志查看

```bash
# Docker 环境
docker-compose logs -f web
docker-compose logs -f worker
docker-compose logs -f redis

# 本地环境
tail -f logs/pulse-guard.log
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/api/health

# 检查 Redis 连接
redis-cli ping

# 检查 Celery Worker
docker-compose exec worker celery -A pulse_guard.worker.celery_app inspect active
```

### 3. 性能监控

推荐使用以下工具：
- **Prometheus + Grafana**: 指标监控
- **ELK Stack**: 日志分析
- **Sentry**: 错误追踪

## 故障排除

### 常见问题

1. **Redis 连接失败**
   ```bash
   # 检查 Redis 是否运行
   redis-cli ping

   # 检查连接配置
   echo $REDIS_URL
   ```

2. **GitHub API 限制**
   ```bash
   # 检查 API 限制状态
   curl -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/rate_limit
   ```

3. **Celery Worker 无响应**
   ```bash
   # 重启 Worker
   docker-compose restart worker

   # 检查任务队列
   docker-compose exec worker celery -A pulse_guard.worker.celery_app inspect active
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   docker stats

   # 调整 Worker 并发数
   docker-compose exec worker celery -A pulse_guard.worker.celery_app control pool_shrink 2
   ```

### 日志级别调整

在 `pulse_guard/main.py` 中调整日志级别：

```python
# 生产环境使用 INFO 级别
logging.basicConfig(level=logging.INFO)

# 开发环境使用 DEBUG 级别
logging.basicConfig(level=logging.DEBUG)
```

### 备份和恢复

```bash
# 备份 Redis 数据
docker-compose exec redis redis-cli BGSAVE

# 备份配置文件
tar -czf pulse-guard-config-$(date +%Y%m%d).tar.gz .env config.toml

# 恢复配置
tar -xzf pulse-guard-config-YYYYMMDD.tar.gz
```

## 更新和维护

### 1. 更新应用

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

### 2. 数据库迁移

如果未来添加数据库支持：

```bash
# 运行迁移
docker-compose exec web python -m alembic upgrade head
```

### 3. 定期维护

```bash
# 清理 Docker 镜像
docker system prune -f

# 清理日志文件
find logs/ -name "*.log" -mtime +30 -delete

# 检查磁盘空间
df -h
```

---

如需更多帮助，请查看项目的 [README.md](README.md) 或提交 Issue。
