# Pulse Guard Docker 部署指南

本文档专门介绍如何使用 Docker 部署 Pulse Guard。

## 快速开始

### 1. 准备环境

```bash
# 克隆项目
git clone <repository-url>
cd pulse-guard

# 复制环境变量文件
cp .env.example .env

# 编辑环境变量 (必须配置 GITHUB_TOKEN 和 DEEPSEEK_API_KEY)
vim .env
```

### 2. 一键部署

```bash
# 开发环境
./deploy.sh dev

# 生产环境
./deploy.sh prod

# 生产环境 + 监控
./deploy.sh prod --monitoring
```

### 3. 验证部署

```bash
# 运行测试脚本
./scripts/test-deployment.sh

# 手动检查
curl http://localhost:8000/api/health
```

## 服务架构

### 开发环境服务

```yaml
services:
  - web: FastAPI 应用 (端口 8000)
  - worker: Celery Worker
  - redis: Redis 消息队列
  - flower: Celery 监控 (端口 5555, 可选)
```

### 生产环境服务

```yaml
services:
  - nginx: 反向代理 (端口 80/443)
  - web: FastAPI 应用 (多实例)
  - worker: Celery Worker (多实例)
  - redis: Redis 消息队列
  - flower: Celery 监控
  - prometheus: 指标收集 (可选)
  - grafana: 监控面板 (可选)
```

## 详细配置

### 环境变量

| 变量 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `GITHUB_TOKEN` | ✓ | - | GitHub API 令牌 |
| `DEEPSEEK_API_KEY` | ✓ | - | Deepseek API 密钥 |
| `REDIS_URL` | - | `redis://redis:6379/0` | Redis 连接 URL |
| `WEB_WORKERS` | - | `1` | Web 服务器进程数 |
| `CELERY_WORKER_CONCURRENCY` | - | `2` | Worker 并发数 |

### 容器配置

#### Web 服务

```yaml
web:
  build: .
  command: ["web"]
  ports:
    - "8000:8000"
  environment:
    - REDIS_URL=redis://redis:6379/0
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
```

#### Worker 服务

```yaml
worker:
  build: .
  command: ["worker"]
  environment:
    - REDIS_URL=redis://redis:6379/0
    - CELERY_WORKER_CONCURRENCY=4
  healthcheck:
    test: ["CMD", "celery", "-A", "pulse_guard.worker.celery_app", "inspect", "ping"]
```

## 常用命令

### 基础操作

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps
```

### 扩展服务

```bash
# 扩展 Worker 实例
docker-compose up -d --scale worker=3

# 扩展 Web 实例 (需要负载均衡)
docker-compose up -d --scale web=2
```

### 维护操作

```bash
# 重新构建镜像
docker-compose build

# 强制重新构建
docker-compose build --no-cache

# 清理未使用的镜像
docker system prune -f

# 查看资源使用
docker stats
```

## 监控和调试

### 查看日志

```bash
# 所有服务日志
docker-compose logs -f

# 特定服务日志
docker-compose logs -f web
docker-compose logs -f worker
docker-compose logs -f redis

# 最近 100 行日志
docker-compose logs --tail=100 web
```

### 进入容器

```bash
# 进入 Web 容器
docker-compose exec web bash

# 进入 Worker 容器
docker-compose exec worker bash

# 进入 Redis 容器
docker-compose exec redis redis-cli
```

### 健康检查

```bash
# 检查所有服务健康状态
docker-compose ps

# 检查应用健康状态
curl http://localhost:8000/api/health

# 检查 Redis
docker-compose exec redis redis-cli ping

# 检查 Celery Worker
docker-compose exec worker celery -A pulse_guard.worker.celery_app inspect active
```

## 生产环境配置

### SSL 证书

```bash
# 使用 Let's Encrypt
certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem docker/ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem docker/ssl/key.pem
```

### 环境变量

```bash
# 使用生产环境模板
cp .env.production .env

# 编辑配置
vim .env
```

### 启动生产服务

```bash
# 基础生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 包含监控
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --profile monitoring up -d
```

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误
   docker-compose logs web
   
   # 检查配置
   docker-compose config
   ```

2. **Redis 连接失败**
   ```bash
   # 检查 Redis 状态
   docker-compose exec redis redis-cli ping
   
   # 检查网络连接
   docker-compose exec web ping redis
   ```

3. **健康检查失败**
   ```bash
   # 检查应用状态
   docker-compose exec web curl http://localhost:8000/api/health
   
   # 检查端口监听
   docker-compose exec web netstat -tlnp
   ```

4. **内存不足**
   ```bash
   # 查看资源使用
   docker stats
   
   # 调整 Worker 并发数
   docker-compose exec worker celery -A pulse_guard.worker.celery_app control pool_shrink 2
   ```

### 性能优化

1. **调整 Worker 数量**
   ```yaml
   # docker-compose.yml
   worker:
     deploy:
       replicas: 3
   ```

2. **调整内存限制**
   ```yaml
   # docker-compose.prod.yml
   web:
     deploy:
       resources:
         limits:
           memory: 1G
   ```

3. **启用 Redis 持久化**
   ```yaml
   redis:
     command: redis-server --appendonly yes
   ```

## 备份和恢复

### 备份数据

```bash
# 备份 Redis 数据
docker-compose exec redis redis-cli BGSAVE
docker cp $(docker-compose ps -q redis):/data/dump.rdb ./backup/

# 备份配置文件
tar -czf backup/config-$(date +%Y%m%d).tar.gz .env config.toml

# 备份日志
tar -czf backup/logs-$(date +%Y%m%d).tar.gz logs/
```

### 恢复数据

```bash
# 恢复 Redis 数据
docker-compose down
docker cp ./backup/dump.rdb $(docker-compose ps -q redis):/data/
docker-compose up -d

# 恢复配置
tar -xzf backup/config-YYYYMMDD.tar.gz
```

## 更新和升级

### 更新应用

```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose build
docker-compose up -d

# 验证更新
./scripts/test-deployment.sh
```

### 滚动更新

```bash
# 逐个重启服务
docker-compose up -d --no-deps web
docker-compose up -d --no-deps worker
```

## 安全建议

1. **使用非 root 用户运行容器**
2. **定期更新基础镜像**
3. **使用 secrets 管理敏感信息**
4. **启用防火墙和访问控制**
5. **定期备份数据**
6. **监控异常活动**

## 相关链接

- [主要部署文档](DEPLOYMENT.md)
- [项目 README](README.md)
- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
