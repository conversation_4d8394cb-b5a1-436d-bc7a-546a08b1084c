#!/bin/bash
set -e

# Pulse Guard Docker 容器启动脚本
# 支持启动不同类型的服务：web, worker, flower

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 等待服务可用
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}
    
    log_info "等待 $service_name 服务启动 ($host:$port)..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" 2>/dev/null; then
            log_info "$service_name 服务已就绪"
            return 0
        fi
        log_debug "等待 $service_name... ($i/$timeout)"
        sleep 1
    done
    
    log_error "$service_name 服务启动超时"
    return 1
}

# 检查必需的环境变量
check_env_vars() {
    local required_vars=("REDIS_URL")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必需的环境变量: ${missing_vars[*]}"
        log_error "请检查 .env 文件或环境变量配置"
        exit 1
    fi
}

# 解析 Redis URL 并等待连接
wait_for_redis() {
    if [[ -n "$REDIS_URL" ]]; then
        # 从 REDIS_URL 解析主机和端口
        # 支持格式: redis://host:port/db 或 redis://host:port
        local redis_host=$(echo "$REDIS_URL" | sed -n 's|redis://\([^:]*\).*|\1|p')
        local redis_port=$(echo "$REDIS_URL" | sed -n 's|redis://[^:]*:\([0-9]*\).*|\1|p')
        
        # 默认值
        redis_host=${redis_host:-localhost}
        redis_port=${redis_port:-6379}
        
        wait_for_service "$redis_host" "$redis_port" "Redis"
    else
        log_warn "REDIS_URL 未设置，跳过 Redis 连接检查"
    fi
}

# 启动 Web 服务
start_web() {
    log_info "启动 Pulse Guard Web 服务..."
    
    # 等待依赖服务
    wait_for_redis
    
    # 启动 FastAPI 应用
    exec uvicorn pulse_guard.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers ${WEB_WORKERS:-1} \
        --log-level ${LOG_LEVEL:-info}
}

# 启动 Celery Worker
start_worker() {
    log_info "启动 Pulse Guard Celery Worker..."
    
    # 等待依赖服务
    wait_for_redis
    
    # 启动 Celery Worker
    exec celery -A pulse_guard.worker.celery_app worker \
        --loglevel=${LOG_LEVEL:-info} \
        --concurrency=${CELERY_WORKER_CONCURRENCY:-2} \
        --max-tasks-per-child=${CELERY_MAX_TASKS_PER_CHILD:-1000} \
        --time-limit=${CELERY_TASK_TIME_LIMIT:-300} \
        --soft-time-limit=${CELERY_TASK_SOFT_TIME_LIMIT:-240}
}

# 启动 Celery Flower
start_flower() {
    log_info "启动 Celery Flower 监控..."
    
    # 等待依赖服务
    wait_for_redis
    
    # 启动 Flower
    exec celery -A pulse_guard.worker.celery_app flower \
        --port=5555 \
        --broker="$REDIS_URL" \
        --basic_auth="${FLOWER_BASIC_AUTH:-admin:admin123}" \
        --url_prefix="${FLOWER_URL_PREFIX:-}"
}

# 运行数据库迁移 (如果需要)
run_migrations() {
    log_info "运行数据库迁移..."
    # 这里可以添加数据库迁移命令
    # 例如: alembic upgrade head
    log_info "暂无数据库迁移需要执行"
}

# 健康检查
health_check() {
    case "$1" in
        web)
            curl -f http://localhost:8000/api/health || exit 1
            ;;
        worker)
            celery -A pulse_guard.worker.celery_app inspect ping || exit 1
            ;;
        redis)
            redis-cli ping || exit 1
            ;;
        *)
            log_error "未知的健康检查类型: $1"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    log_info "Pulse Guard 容器启动中..."
    log_info "服务类型: ${1:-未指定}"
    
    # 检查环境变量
    check_env_vars
    
    # 根据参数启动不同服务
    case "$1" in
        web)
            start_web
            ;;
        worker)
            start_worker
            ;;
        flower)
            start_flower
            ;;
        migrate)
            run_migrations
            ;;
        health)
            health_check "$2"
            ;;
        *)
            log_error "用法: $0 {web|worker|flower|migrate|health}"
            log_error "可用的服务类型:"
            log_error "  web    - 启动 FastAPI Web 服务"
            log_error "  worker - 启动 Celery Worker"
            log_error "  flower - 启动 Celery Flower 监控"
            log_error "  migrate - 运行数据库迁移"
            log_error "  health - 健康检查"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
