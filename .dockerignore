# Git 相关
.git
.gitignore
.gitattributes

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.uv/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/
*.out

# 临时文件
*.tmp
*.temp
.cache/
.pytest_cache/
.coverage
.nyc_output

# 文档
*.md
docs/
README*
CHANGELOG*
LICENSE*

# 配置文件 (保留示例文件)
.env.local
.env.development
.env.test
.env.production

# Docker 相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 测试相关
tests/
test_*
*_test.py
.tox/
.coverage
htmlcov/

# 其他
*.bak
*.orig
*.rej
.mypy_cache/
.dmypy.json
dmypy.json
