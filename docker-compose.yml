version: '3.8'

services:
  # Redis 服务 - 消息队列和缓存
  redis:
    image: redis:7-alpine
    container_name: pulse-guard-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Web 服务 - FastAPI 应用
  web:
    build: .
    container_name: pulse-guard-web
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./config.toml:/app/config.toml:ro
    depends_on:
      redis:
        condition: service_healthy
    command: ["web"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Worker 服务 - 后台任务处理
  worker:
    build: .
    container_name: pulse-guard-worker
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./config.toml:/app/config.toml:ro
    depends_on:
      redis:
        condition: service_healthy
    command: ["worker"]
    healthcheck:
      test: ["CMD", "celery", "-A", "pulse_guard.worker.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Flower 服务 - 任务监控 (可选)
  flower:
    build: .
    container_name: pulse-guard-flower
    restart: unless-stopped
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./config.toml:/app/config.toml:ro
    depends_on:
      redis:
        condition: service_healthy
    command: ["flower"]
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local

networks:
  default:
    name: pulse-guard-network
