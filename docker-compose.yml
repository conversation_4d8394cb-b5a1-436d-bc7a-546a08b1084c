version: '3.8'

services:
  # Redis 服务
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Web 服务
  web:
    build: .
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - SERVICE_TYPE=web
    env_file:
      - .env
    depends_on:
      - redis

  # Worker 服务
  worker:
    build: .
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis:6379/0
      - SERVICE_TYPE=worker
    env_file:
      - .env
    depends_on:
      - redis

volumes:
  redis_data:
