# 生产环境配置模板
# 复制此文件为 .env 并填入实际值

# ================================
# 基础配置
# ================================

# 环境标识
ENVIRONMENT=production

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ================================
# GitHub 配置
# ================================

# GitHub API Token (必需)
# 获取方式: GitHub Settings > Developer settings > Personal access tokens
# 需要权限: repo, read:org, read:user
GITHUB_TOKEN=ghp_your_github_token_here

# GitHub Webhook Secret (强烈推荐)
# 用于验证 webhook 请求的真实性
WEBHOOK_SECRET=your_secure_webhook_secret_here

# ================================
# Gitee 配置 (可选)
# ================================

# Gitee Access Token (如果使用 Gitee)
GITEE_ACCESS_TOKEN=your_gitee_access_token_here

# Gitee Webhook Secret (如果使用 Gitee)
GITEE_WEBHOOK_SECRET=your_gitee_webhook_secret_here

# ================================
# LLM 配置
# ================================

# Deepseek API Key (必需)
# 获取方式: https://platform.deepseek.com/
DEEPSEEK_API_KEY=sk-your_deepseek_api_key_here

# OpenAI API Key (备用，如果使用 OpenAI)
# OPENAI_API_KEY=sk-your_openai_api_key_here

# ================================
# Redis 配置
# ================================

# Redis 连接 URL
# 格式: redis://[username:password@]host:port/db
REDIS_URL=redis://localhost:6379/0

# 如果使用 Redis 密码认证
# REDIS_URL=redis://:password@localhost:6379/0

# 如果使用 Redis Cluster
# REDIS_URL=redis://node1:6379,node2:6379,node3:6379/0

# ================================
# 应用配置
# ================================

# Web 服务器配置
WEB_WORKERS=2
WEB_HOST=0.0.0.0
WEB_PORT=8000

# Celery Worker 配置
CELERY_WORKER_CONCURRENCY=4
CELERY_MAX_TASKS_PER_CHILD=1000
CELERY_TASK_TIME_LIMIT=300
CELERY_TASK_SOFT_TIME_LIMIT=240

# ================================
# 监控配置
# ================================

# Flower 监控界面认证
FLOWER_PASSWORD=your_secure_flower_password_here

# Grafana 管理员密码
GRAFANA_PASSWORD=your_secure_grafana_password_here

# ================================
# LangSmith 配置 (可选)
# ================================

# LangSmith 追踪 (用于调试和监控)
LANGSMITH_TRACING=true
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGSMITH_PROJECT=pulse-guard-production

# ================================
# 安全配置
# ================================

# 允许的主机 (用于 CORS)
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# SSL/TLS 配置
SSL_CERT_PATH=/etc/ssl/certs/pulse-guard.crt
SSL_KEY_PATH=/etc/ssl/private/pulse-guard.key

# ================================
# 数据库配置 (如果未来需要)
# ================================

# PostgreSQL (示例)
# DATABASE_URL=postgresql://user:password@localhost:5432/pulse_guard

# MySQL (示例)
# DATABASE_URL=mysql://user:password@localhost:3306/pulse_guard

# ================================
# 外部服务配置
# ================================

# Sentry 错误追踪 (可选)
# SENTRY_DSN=https://<EMAIL>/project-id

# 邮件服务配置 (可选，用于通知)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true

# ================================
# 备份配置 (可选)
# ================================

# 备份存储
# BACKUP_S3_BUCKET=pulse-guard-backups
# BACKUP_S3_ACCESS_KEY=your-s3-access-key
# BACKUP_S3_SECRET_KEY=your-s3-secret-key
# BACKUP_S3_REGION=us-east-1

# ================================
# 性能配置
# ================================

# 请求限制
RATE_LIMIT_PER_MINUTE=60
WEBHOOK_RATE_LIMIT_PER_MINUTE=30

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ================================
# 调试配置 (生产环境应禁用)
# ================================

# 调试模式 (生产环境必须为 false)
DEBUG=false

# 详细错误信息 (生产环境应禁用)
SHOW_ERROR_DETAILS=false
