#!/bin/bash

# Pulse Guard 部署测试脚本
# 用于验证部署是否成功

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 配置
BASE_URL="${BASE_URL:-http://localhost:8000}"
TIMEOUT=30

# 测试函数
test_endpoint() {
    local endpoint="$1"
    local expected_status="${2:-200}"
    local description="$3"
    
    log_info "测试: $description"
    log_info "URL: $BASE_URL$endpoint"
    
    local response
    local status_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT "$BASE_URL$endpoint" || echo -e "\n000")
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [[ "$status_code" == "$expected_status" ]]; then
        log_success "✓ $description - 状态码: $status_code"
        if [[ -n "$body" ]] && [[ "$body" != "null" ]]; then
            echo "  响应: $(echo "$body" | head -c 100)..."
        fi
        return 0
    else
        log_error "✗ $description - 期望: $expected_status, 实际: $status_code"
        if [[ -n "$body" ]]; then
            echo "  响应: $body"
        fi
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local max_attempts=30
    local attempt=1
    
    log_info "等待服务启动..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s --max-time 5 "$BASE_URL/" > /dev/null 2>&1; then
            log_success "服务已启动"
            return 0
        fi
        
        log_info "等待中... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "服务启动超时"
    return 1
}

# 检查 Docker 服务状态
check_docker_services() {
    log_info "检查 Docker 服务状态..."
    
    if ! command -v docker-compose &> /dev/null; then
        log_warn "docker-compose 未安装，跳过容器状态检查"
        return 0
    fi
    
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker 服务正在运行"
        docker-compose ps
    else
        log_warn "Docker 服务可能未启动"
        docker-compose ps
    fi
}

# 测试 Redis 连接
test_redis() {
    log_info "测试 Redis 连接..."
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping > /dev/null 2>&1; then
            log_success "Redis 连接正常"
        else
            log_warn "Redis 连接失败 (可能在容器中运行)"
        fi
    else
        log_warn "redis-cli 未安装，跳过 Redis 测试"
    fi
}

# 主测试函数
run_tests() {
    local failed_tests=0
    
    log_info "开始 Pulse Guard 部署测试"
    log_info "基础 URL: $BASE_URL"
    echo
    
    # 检查 Docker 服务
    check_docker_services
    echo
    
    # 等待服务启动
    if ! wait_for_service; then
        log_error "服务未启动，测试终止"
        exit 1
    fi
    echo
    
    # 测试基础端点
    test_endpoint "/" 200 "根路径" || ((failed_tests++))
    echo
    
    # 测试健康检查
    test_endpoint "/api/health" 200 "健康检查" || ((failed_tests++))
    echo
    
    # 测试 API 文档
    test_endpoint "/docs" 200 "API 文档" || ((failed_tests++))
    echo
    
    # 测试 OpenAPI 规范
    test_endpoint "/openapi.json" 200 "OpenAPI 规范" || ((failed_tests++))
    echo
    
    # 测试不存在的端点
    test_endpoint "/nonexistent" 404 "404 错误处理" || ((failed_tests++))
    echo
    
    # 测试 Redis
    test_redis
    echo
    
    # 总结
    if [[ $failed_tests -eq 0 ]]; then
        log_success "所有测试通过! 🎉"
        log_info "部署验证成功"
    else
        log_error "$failed_tests 个测试失败"
        log_error "部署可能存在问题"
        exit 1
    fi
}

# 显示帮助
show_help() {
    cat << EOF
Pulse Guard 部署测试脚本

用法: $0 [选项]

选项:
  -u, --url URL     指定基础 URL (默认: http://localhost:8000)
  -t, --timeout N   设置超时时间 (默认: 30 秒)
  -h, --help        显示此帮助信息

环境变量:
  BASE_URL          基础 URL (默认: http://localhost:8000)

示例:
  $0                                    # 测试本地部署
  $0 -u http://localhost:8000           # 指定 URL
  $0 -u https://your-domain.com         # 测试生产环境
  BASE_URL=http://localhost:8000 $0     # 使用环境变量

EOF
}

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行测试
run_tests
