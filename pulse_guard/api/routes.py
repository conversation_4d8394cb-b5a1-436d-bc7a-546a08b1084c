"""
API 路由定义模块。
"""
from fastapi import APIRouter, Request

from pulse_guard.api.gitee_webhook import handle_webhook as handle_gitee_webhook
from pulse_guard.api.github_webhook import handle_webhook as handle_github_webhook
from pulse_guard.worker.tasks import process_pull_request

# 创建路由器
router = APIRouter()


@router.post("/webhook/github")
async def github_webhook(request: Request):
    """GitHub Webhook 端点"""
    return await handle_github_webhook(request)


@router.post("/webhook/gitee")
async def gitee_webhook(request: Request):
    """Gitee Webhook 端点"""
    return await handle_gitee_webhook(request)


@router.post("/review")
async def manual_review(repo: str, pr_number: int, platform: str = "github"):
    """手动触发代码审查"""
    # 启动异步任务
    task = process_pull_request.delay(repo=repo, pr_number=pr_number, platform=platform)

    return {
        "status": "success",
        "message": f"Processing PR #{pr_number} from {repo}",
        "task_id": task.id
    }


@router.get("/health")
async def health_check():
    """健康检查端点"""
    import redis
    from pulse_guard.config import config

    health_status = {
        "status": "ok",
        "timestamp": __import__("datetime").datetime.utcnow().isoformat(),
        "version": "0.1.0",
        "services": {}
    }

    # 检查 Redis 连接
    try:
        redis_client = redis.from_url(config.redis.url)
        redis_client.ping()
        health_status["services"]["redis"] = {"status": "healthy"}
    except Exception as e:
        health_status["services"]["redis"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "degraded"

    # 检查配置
    try:
        if not config.github.token:
            health_status["services"]["github_config"] = {"status": "warning", "message": "GitHub token not configured"}
        else:
            health_status["services"]["github_config"] = {"status": "healthy"}

        if not config.llm.api_key:
            health_status["services"]["llm_config"] = {"status": "warning", "message": "LLM API key not configured"}
        else:
            health_status["services"]["llm_config"] = {"status": "healthy"}
    except Exception as e:
        health_status["services"]["config"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "degraded"

    return health_status
