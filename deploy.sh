#!/bin/bash

# Pulse Guard 快速部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Pulse Guard 部署脚本

用法: $0 [选项] <环境>

环境:
  dev         开发环境部署
  prod        生产环境部署
  stop        停止所有服务
  restart     重启所有服务
  logs        查看日志
  status      查看服务状态

选项:
  -h, --help     显示此帮助信息
  -v, --verbose  详细输出
  -f, --force    强制重新构建镜像
  --monitoring   启用监控服务 (Prometheus, Grafana)

示例:
  $0 dev                    # 开发环境部署
  $0 prod --monitoring      # 生产环境部署并启用监控
  $0 restart                # 重启服务
  $0 logs web               # 查看 web 服务日志

EOF
}

# 检查依赖
check_dependencies() {
    local deps=("docker" "docker-compose")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing+=("$dep")
        fi
    done
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing[*]}"
        log_error "请先安装 Docker 和 Docker Compose"
        exit 1
    fi
}

# 检查环境文件
check_env_file() {
    if [[ ! -f .env ]]; then
        if [[ -f .env.example ]]; then
            log_warn ".env 文件不存在，从 .env.example 复制"
            cp .env.example .env
            log_warn "请编辑 .env 文件并填入正确的配置"
        else
            log_error ".env 和 .env.example 文件都不存在"
            exit 1
        fi
    fi
}

# 创建必要的目录
create_directories() {
    local dirs=("logs" "docker/ssl")
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi
    done
}

# 生成自签名 SSL 证书 (仅用于测试)
generate_ssl_cert() {
    local ssl_dir="docker/ssl"
    
    if [[ ! -f "$ssl_dir/cert.pem" ]] || [[ ! -f "$ssl_dir/key.pem" ]]; then
        log_info "生成自签名 SSL 证书..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$ssl_dir/key.pem" \
            -out "$ssl_dir/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost" \
            2>/dev/null || {
                log_warn "无法生成 SSL 证书，请手动创建或使用 HTTP"
            }
    fi
}

# 开发环境部署
deploy_dev() {
    log_info "开始开发环境部署..."
    
    local compose_args=()
    
    if [[ "$FORCE_BUILD" == "true" ]]; then
        compose_args+=("--build")
    fi
    
    if [[ "$ENABLE_MONITORING" == "true" ]]; then
        compose_args+=("--profile" "monitoring")
    fi
    
    docker-compose up -d "${compose_args[@]}"
    
    log_info "开发环境部署完成"
    log_info "Web 服务: http://localhost:8000"
    log_info "Redis: localhost:6379"
    
    if [[ "$ENABLE_MONITORING" == "true" ]]; then
        log_info "Flower 监控: http://localhost:5555"
    fi
}

# 生产环境部署
deploy_prod() {
    log_info "开始生产环境部署..."
    
    # 生成 SSL 证书
    generate_ssl_cert
    
    local compose_args=("-f" "docker-compose.yml" "-f" "docker-compose.prod.yml")
    
    if [[ "$FORCE_BUILD" == "true" ]]; then
        compose_args+=("--build")
    fi
    
    if [[ "$ENABLE_MONITORING" == "true" ]]; then
        compose_args+=("--profile" "monitoring")
    fi
    
    docker-compose "${compose_args[@]}" up -d
    
    log_info "生产环境部署完成"
    log_info "Web 服务: https://localhost (通过 Nginx)"
    log_info "HTTP 重定向: http://localhost -> https://localhost"
    
    if [[ "$ENABLE_MONITORING" == "true" ]]; then
        log_info "Flower 监控: https://localhost/flower/"
        log_info "Prometheus: http://localhost:9090"
        log_info "Grafana: http://localhost:3000"
    fi
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    
    # 尝试停止生产环境
    if docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps -q &>/dev/null; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
    fi
    
    # 停止开发环境
    docker-compose down
    
    log_info "所有服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    
    # 根据现有配置重新启动
    if [[ -f docker-compose.prod.yml ]] && docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps -q &>/dev/null; then
        deploy_prod
    else
        deploy_dev
    fi
}

# 查看日志
show_logs() {
    local service="$1"
    
    if [[ -n "$service" ]]; then
        log_info "查看 $service 服务日志..."
        docker-compose logs -f "$service"
    else
        log_info "查看所有服务日志..."
        docker-compose logs -f
    fi
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    log_info "\n容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# 主函数
main() {
    local environment=""
    local service=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                set -x
                shift
                ;;
            -f|--force)
                FORCE_BUILD="true"
                shift
                ;;
            --monitoring)
                ENABLE_MONITORING="true"
                shift
                ;;
            dev|prod|stop|restart|logs|status)
                environment="$1"
                shift
                ;;
            *)
                if [[ "$environment" == "logs" ]] && [[ -z "$service" ]]; then
                    service="$1"
                fi
                shift
                ;;
        esac
    done
    
    # 检查环境参数
    if [[ -z "$environment" ]]; then
        log_error "请指定环境: dev, prod, stop, restart, logs, status"
        show_help
        exit 1
    fi
    
    # 检查依赖
    check_dependencies
    
    # 执行相应操作
    case "$environment" in
        dev)
            check_env_file
            create_directories
            deploy_dev
            ;;
        prod)
            check_env_file
            create_directories
            deploy_prod
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs "$service"
            ;;
        status)
            show_status
            ;;
        *)
            log_error "未知环境: $environment"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
